import '../utils/app_logger.dart';
import '../services/aws_media_service.dart';

enum ReflexType { flare, customImage }

extension ReflexTypeExtension on ReflexType {
  String get value {
    switch (this) {
      case ReflexType.flare:
        return 'flare';
      case ReflexType.customImage:
        return 'custom_image';
    }
  }

  static ReflexType fromString(String value) {
    switch (value) {
      case 'flare':
        return ReflexType.flare;
      case 'custom_image':
        return ReflexType.customImage;
      default:
        return ReflexType.flare;
    }
  }
}

class ReflexModel {
  final String id;
  final String postId;
  final String userId;
  final String? mediaId;
  final Map<String, dynamic>? flareData;
  final String? textOverlay;
  final ReflexType reflexType;
  final int likes;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  // User information (from join)
  final String? username;
  final String? displayName;
  final String? avatarUrl;

  // Like status for current user
  final bool isLikedByCurrentUser;

  ReflexModel({
    required this.id,
    required this.postId,
    required this.userId,
    this.mediaId,
    this.flareData,
    this.textOverlay,
    required this.reflexType,
    required this.likes,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.username,
    this.displayName,
    this.avatarUrl,
    this.isLikedByCurrentUser = false,
  });

  factory ReflexModel.fromJson(Map<String, dynamic> json) {
    AppLogger.debug(
      'ReflexModel.fromJson: Starting to parse reflex ${json['id']}',
    );
    try {
      final reflexType = ReflexTypeExtension.fromString(
        json['reflexType'] as String? ??
            json['reflex_type'] as String? ??
            'flare',
      );

      return ReflexModel(
        id: json['id'] as String,
        postId: json['postId'] as String? ?? json['post_id'] as String,
        userId: json['userId'] as String? ?? json['user_id'] as String,
        mediaId: json['mediaId'] as String? ?? json['media_id'] as String?,
        flareData:
            json['flareData'] as Map<String, dynamic>? ??
            json['flare_data'] as Map<String, dynamic>?,
        textOverlay:
            json['textOverlay'] as String? ?? json['text_overlay'] as String?,
        reflexType: reflexType,
        likes: json['likes'] as int? ?? 0,
        isActive:
            json['isActive'] as bool? ?? json['is_active'] as bool? ?? true,
        createdAt: DateTime.parse(
          json['createdAt'] as String? ?? json['created_at'] as String,
        ),
        updatedAt: DateTime.parse(
          json['updatedAt'] as String? ?? json['updated_at'] as String,
        ),
        username: json['username'] as String?,
        displayName:
            json['displayName'] as String? ?? json['display_name'] as String?,
        avatarUrl:
            json['avatarUrl'] as String? ?? json['avatar_url'] as String?,
        isLikedByCurrentUser: json['isLikedByCurrentUser'] as bool? ?? false,
      );
    } catch (e, stackTrace) {
      AppLogger.error(
        'ReflexModel.fromJson: Error parsing reflex',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error('ReflexModel.fromJson: JSON: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'postId': postId,
      'userId': userId,
      'mediaId': mediaId,
      'flareData': flareData,
      'textOverlay': textOverlay,
      'reflexType': reflexType.value,
      'likes': likes,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'username': username,
      'displayName': displayName,
      'avatarUrl': avatarUrl,
      'isLikedByCurrentUser': isLikedByCurrentUser,
    };
  }

  ReflexModel copyWith({
    String? id,
    String? postId,
    String? userId,
    String? mediaId,
    Map<String, dynamic>? flareData,
    String? textOverlay,
    ReflexType? reflexType,
    int? likes,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? username,
    String? displayName,
    String? avatarUrl,
    bool? isLikedByCurrentUser,
  }) {
    return ReflexModel(
      id: id ?? this.id,
      postId: postId ?? this.postId,
      userId: userId ?? this.userId,
      mediaId: mediaId ?? this.mediaId,
      flareData: flareData ?? this.flareData,
      textOverlay: textOverlay ?? this.textOverlay,
      reflexType: reflexType ?? this.reflexType,
      likes: likes ?? this.likes,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      username: username ?? this.username,
      displayName: displayName ?? this.displayName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      isLikedByCurrentUser: isLikedByCurrentUser ?? this.isLikedByCurrentUser,
    );
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 7) {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String get authorDisplayName {
    if (displayName != null && displayName!.isNotEmpty) {
      return displayName!;
    }
    if (username != null && username!.isNotEmpty) {
      return username!;
    }
    return 'User';
  }

  String get authorUsername {
    if (username != null && username!.isNotEmpty) {
      return '@${username!}';
    }
    return '@user';
  }

  /// Get media URL for this reflex if it has media
  Future<String?> getMediaUrl() async {
    if (mediaId == null) return null;

    try {
      final mediaInfo = await AwsMediaService.instance.getMedia(mediaId!);
      if (mediaInfo != null) {
        final url = mediaInfo['url'] as String?;
        AppLogger.debug('ReflexModel: Got media URL for reflex $id: $url');
        return url;
      }
    } catch (e) {
      AppLogger.error(
        'ReflexModel: Error getting media URL for reflex $id',
        error: e,
      );
    }
    return null;
  }

  @override
  String toString() {
    return 'ReflexModel(id: $id, postId: $postId, author: $authorDisplayName, type: ${reflexType.value})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ReflexModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class CreateReflexRequest {
  final String postId;
  final String? mediaId;
  final Map<String, dynamic>? flareData;
  final String? textOverlay;
  final ReflexType reflexType;

  CreateReflexRequest({
    required this.postId,
    this.mediaId,
    this.flareData,
    this.textOverlay,
    this.reflexType = ReflexType.flare,
  });

  Map<String, dynamic> toJson() {
    return {
      'postId': postId,
      'mediaId': mediaId,
      'flareData': flareData,
      'textOverlay': textOverlay,
      'reflexType': reflexType.value,
    };
  }
}
