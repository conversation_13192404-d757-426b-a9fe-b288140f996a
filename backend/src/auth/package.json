{"name": "gameflex-auth-lambda", "version": "1.0.0", "description": "GameFlex Authentication Lambda Functions", "main": "index.js", "scripts": {"build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.600.0", "@aws-sdk/client-dynamodb": "^3.600.0", "@aws-sdk/lib-dynamodb": "^3.600.0", "@types/node-jose": "^1.1.13", "apple-auth": "^1.0.9", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "node-jose": "^2.2.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/aws-lambda": "^8.10.140", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20.14.0", "@types/uuid": "^10.0.0", "typescript": "^5.5.0"}}