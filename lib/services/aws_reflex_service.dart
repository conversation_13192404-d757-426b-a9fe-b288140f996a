import 'dart:developer' as developer;
import '../models/reflex_model.dart';
import '../utils/app_logger.dart';
import 'api_service.dart';

class AwsReflexService {
  static final AwsReflexService _instance = AwsReflexService._internal();
  factory AwsReflexService() => _instance;
  AwsReflexService._internal();

  static AwsReflexService get instance => _instance;

  /// Get reflexes for a specific post
  Future<List<ReflexModel>> getReflexesForPost(String postId) async {
    try {
      developer.log('AwsReflexService: Getting reflexes for post $postId');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/posts/$postId/reflexes',
      );

      final data = ApiService.instance.parseResponse(response);
      final reflexesData = data['reflexes'] as List<dynamic>?;

      if (reflexesData != null) {
        final reflexes =
            reflexesData
                .map(
                  (reflexData) =>
                      ReflexModel.fromJson(reflexData as Map<String, dynamic>),
                )
                .toList();

        developer.log(
          'AwsReflexService: Retrieved ${reflexes.length} reflexes for post $postId',
        );
        return reflexes;
      }

      developer.log('AwsReflexService: No reflexes found for post $postId');
      return [];
    } catch (e, stackTrace) {
      AppLogger.error(
        'AwsReflexService: Error getting reflexes for post $postId',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Create a new reflex for a post
  Future<ReflexModel?> createReflex(CreateReflexRequest request) async {
    try {
      developer.log(
        'AwsReflexService: Creating reflex for post ${request.postId}',
      );

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'POST',
        path: '/posts/${request.postId}/reflexes',
        body: request.toJson(),
      );

      final data = ApiService.instance.parseResponse(response);
      final reflexData = data['reflex'] as Map<String, dynamic>?;

      if (reflexData != null) {
        final reflex = ReflexModel.fromJson(reflexData);
        developer.log('AwsReflexService: Created reflex ${reflex.id}');
        return reflex;
      }

      developer.log(
        'AwsReflexService: Failed to create reflex - no reflex data in response',
      );
      return null;
    } catch (e, stackTrace) {
      AppLogger.error(
        'AwsReflexService: Error creating reflex',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get a specific reflex by ID
  Future<ReflexModel?> getReflex(String reflexId) async {
    try {
      developer.log('AwsReflexService: Getting reflex $reflexId');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/reflexes/$reflexId',
      );

      final data = ApiService.instance.parseResponse(response);
      final reflexData = data['reflex'] as Map<String, dynamic>?;

      if (reflexData != null) {
        final reflex = ReflexModel.fromJson(reflexData);
        developer.log('AwsReflexService: Retrieved reflex ${reflex.id}');
        return reflex;
      }

      developer.log('AwsReflexService: Reflex $reflexId not found');
      return null;
    } catch (e, stackTrace) {
      AppLogger.error(
        'AwsReflexService: Error getting reflex $reflexId',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Update a reflex
  Future<ReflexModel?> updateReflex(
    String reflexId,
    Map<String, dynamic> updates,
  ) async {
    try {
      developer.log('AwsReflexService: Updating reflex $reflexId');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'PUT',
        path: '/reflexes/$reflexId',
        body: updates,
      );

      final data = ApiService.instance.parseResponse(response);
      final reflexData = data['reflex'] as Map<String, dynamic>?;

      if (reflexData != null) {
        final reflex = ReflexModel.fromJson(reflexData);
        developer.log('AwsReflexService: Updated reflex ${reflex.id}');
        return reflex;
      }

      developer.log('AwsReflexService: Failed to update reflex $reflexId');
      return null;
    } catch (e, stackTrace) {
      AppLogger.error(
        'AwsReflexService: Error updating reflex $reflexId',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Delete a reflex
  Future<bool> deleteReflex(String reflexId) async {
    try {
      developer.log('AwsReflexService: Deleting reflex $reflexId');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'DELETE',
        path: '/reflexes/$reflexId',
      );

      final statusCode = response.statusCode;
      if (statusCode >= 200 && statusCode < 300) {
        developer.log(
          'AwsReflexService: Successfully deleted reflex $reflexId',
        );
        return true;
      }

      developer.log(
        'AwsReflexService: Failed to delete reflex $reflexId - status code: $statusCode',
      );
      return false;
    } catch (e, stackTrace) {
      AppLogger.error(
        'AwsReflexService: Error deleting reflex $reflexId',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }
}
