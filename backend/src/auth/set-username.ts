import {
    DynamoDBClient
} from '@aws-sdk/client-dynamodb';
import {
    DynamoDBDocumentClient,
    UpdateCommand,
    QueryCommand,
    GetCommand,
    type UpdateCommandInput,
    type QueryCommandInput,
    type GetCommandInput
} from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';

// Types
interface SetUsernameRequest {
    username: string;
}

interface UserRecord {
    id: string;
    email: string;
    username?: string;
    firstName: string;
    lastName: string;
    cognitoUserId: string;
    createdAt: string;
    updatedAt: string;
}

// Configure AWS SDK
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

if (process.env.AWS_SAM_LOCAL) {
    console.log('Running in SAM Local mode');
}

const dynamoClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamoClient);

// Environment variables
const USERS_TABLE = process.env.USERS_TABLE || process.env.USERSTABLE_TABLE_NAME;

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'OPTIONS,POST,GET,PUT,DELETE'
    },
    body: JSON.stringify(body)
});

// Helper function to get user ID from context
const getUserIdFromContext = (event: APIGatewayProxyEvent): string | null => {
    return event.requestContext?.authorizer?.userId || null;
};

// Set username handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('SetUsername Event:', JSON.stringify(event, null, 2));

    // Handle CORS preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return createResponse(200, {});
    }

    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { username }: SetUsernameRequest = JSON.parse(event.body);

        if (!username) {
            return createResponse(400, { error: 'Username is required' });
        }

        // Validate username format
        const usernameRegex = /^[a-zA-Z0-9_-]+$/;
        if (!usernameRegex.test(username) || username.length < 3 || username.length > 30) {
            return createResponse(400, {
                error: 'Invalid username',
                details: 'Username must be 3-30 characters long and contain only alphanumeric characters, underscores, and hyphens'
            });
        }

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        if (!USERS_TABLE) {
            return createResponse(500, { error: 'Users table configuration missing' });
        }

        // Get current user to verify they exist
        const getUserCommand = new GetCommand({
            TableName: USERS_TABLE,
            Key: { id: userId }
        });

        const userResult = await dynamodb.send(getUserCommand);

        if (!userResult.Item) {
            return createResponse(404, { error: 'User not found' });
        }

        const user = userResult.Item as UserRecord;

        // Check if user already has a username
        if (user.username) {
            return createResponse(409, {
                error: 'Username already set',
                details: 'You already have a username. Contact support if you need to change it.'
            });
        }

        // Check if username already exists
        const checkUsernameCommand = new QueryCommand({
            TableName: USERS_TABLE,
            IndexName: 'UsernameIndex',
            KeyConditionExpression: 'username = :username',
            ExpressionAttributeValues: {
                ':username': username
            }
        });

        const existingUserResult = await dynamodb.send(checkUsernameCommand);

        if (existingUserResult.Items && existingUserResult.Items.length > 0) {
            return createResponse(409, {
                error: 'Username already taken',
                details: 'This username is already in use. Please choose a different one.'
            });
        }

        // Update user with username
        const updateCommand = new UpdateCommand({
            TableName: USERS_TABLE,
            Key: { id: userId },
            UpdateExpression: 'SET username = :username, updatedAt = :updatedAt',
            ExpressionAttributeValues: {
                ':username': username,
                ':updatedAt': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        });

        const updateResult = await dynamodb.send(updateCommand);
        const updatedUser = updateResult.Attributes as UserRecord;

        return createResponse(200, {
            message: 'Username set successfully',
            user: {
                id: updatedUser.id,
                email: updatedUser.email,
                username: updatedUser.username,
                firstName: updatedUser.firstName,
                lastName: updatedUser.lastName
            }
        });

    } catch (error: any) {
        console.error('SetUsername error:', error);

        // Handle specific DynamoDB errors
        if (error.name === 'ConditionalCheckFailedException') {
            return createResponse(409, {
                error: 'Username conflict',
                details: 'This username is already taken or there was a conflict updating your account.'
            });
        } else if (error.name === 'ValidationException') {
            return createResponse(400, {
                error: 'Invalid request',
                details: 'Please check your input and try again.'
            });
        } else {
            return createResponse(500, {
                error: 'Failed to set username',
                details: 'An unexpected error occurred. Please try again.'
            });
        }
    }
};
