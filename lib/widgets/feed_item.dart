import 'package:flutter/material.dart';
import '../models/post_model.dart';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';
import 'video_player_widget.dart';

class FeedItem extends StatefulWidget {
  final PostModel post;
  final bool isVisible;

  const FeedItem({super.key, required this.post, this.isVisible = true});

  @override
  State<FeedItem> createState() => _FeedItemState();
}

class _FeedItemState extends State<FeedItem> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: _buildMediaContent(),
    );
  }

  Widget _buildMediaContent() {
    AppLogger.debug('FeedItem: Building media content for post ${widget.post.id}');
    AppLogger.debug('FeedItem: hasMedia: ${widget.post.hasMedia}');
    AppLogger.debug('FeedItem: mediaType: ${widget.post.mediaType}');
    AppLogger.debug('FeedItem: isImage: ${widget.post.isImage}');

    if (widget.post.hasMedia) {
      return FutureBuilder<String?>(
        future: widget.post.getMediaUrl(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
              ),
            );
          }

          final mediaUrl = snapshot.data;
          if (mediaUrl != null) {
            if (widget.post.isImage) {
              AppLogger.debug('FeedItem: Rendering image with URL: $mediaUrl');
              return SizedBox(
                width: double.infinity,
                height: double.infinity,
                child: Image.network(
                  mediaUrl,
                  fit: BoxFit.fitWidth, // Fill full width, crop height if needed
                  errorBuilder: (context, error, stackTrace) {
                    AppLogger.error('FeedItem: Error loading image', error: error);
                    AppLogger.error('FeedItem: URL was: $mediaUrl');
                    return _buildMediaPlaceholder('Image');
                  },
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) {
                      AppLogger.debug('FeedItem: Image loaded successfully: $mediaUrl');
                      return child;
                    }
                    AppLogger.debug('FeedItem: Loading image: ${loadingProgress.cumulativeBytesLoaded}/${loadingProgress.expectedTotalBytes}');
                    return Center(
                      child: CircularProgressIndicator(
                        value: loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded /
                                loadingProgress.expectedTotalBytes!
                            : null,
                        valueColor: const AlwaysStoppedAnimation<Color>(
                          AppColors.gfGreen,
                        ),
                      ),
                    );
                  },
                ),
              );
            } else if (widget.post.isVideo) {
              AppLogger.debug('FeedItem: Rendering video with URL: $mediaUrl');
              // Use the video player widget
              return VideoPlayerWidget(
                videoUrl: mediaUrl,
                autoPlay: true,
                isVisible: widget.isVisible,
              );
            }
          } else {
            AppLogger.debug('FeedItem: No media URL available');
          }

          // Fallback to a gradient background
          return Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [AppColors.gfTeal, AppColors.gfDarkBlue],
              ),
            ),
          );
        },
      );
    } else {
      AppLogger.debug('FeedItem: No media to display');
    }

    // Fallback to a gradient background
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.gfTeal, AppColors.gfDarkBlue],
        ),
      ),
    );
  }

  Widget _buildMediaPlaceholder(String type) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.gfTeal, AppColors.gfDarkBlue],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              type == 'Video' ? Icons.play_circle_outline : Icons.image,
              size: 64,
              color: Colors.white70,
            ),
            const SizedBox(height: 16),
            Text(
              '$type Content',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
