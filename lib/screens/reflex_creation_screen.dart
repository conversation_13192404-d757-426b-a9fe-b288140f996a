import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pro_image_editor/pro_image_editor.dart';
import 'package:image_picker/image_picker.dart';
import '../models/post_model.dart';
import '../models/reflex_model.dart';
import '../providers/reflex_provider.dart';
import '../providers/posts_provider.dart';
import '../services/aws_media_service.dart';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';

class ReflexCreationScreen extends StatefulWidget {
  final PostModel originalPost;

  const ReflexCreationScreen({super.key, required this.originalPost});

  @override
  State<ReflexCreationScreen> createState() => _ReflexCreationScreenState();
}

class _ReflexCreationScreenState extends State<ReflexCreationScreen> {
  bool _isCreating = false;
  Uint8List? _editedImageBytes;
  String? _uploadedMediaId;

  @override
  void initState() {
    super.initState();
    // Automatically open image source dialog when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showImageSourceDialog();
    });
  }

  Future<void> _showImageSourceDialog() async {
    if (!mounted) return;

    final choice = await showDialog<String>(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppColors.gfDarkBackground,
            title: const Text(
              'Choose Image Source',
              style: TextStyle(color: AppColors.gfOffWhite),
            ),
            content: const Text(
              'Would you like to edit the original post image or select a new image?',
              style: TextStyle(color: AppColors.gfGrayText),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop('original'),
                child: const Text(
                  'Original Image',
                  style: TextStyle(color: AppColors.gfGreen),
                ),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop('new'),
                child: const Text(
                  'New Image',
                  style: TextStyle(color: AppColors.gfTeal),
                ),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop('cancel'),
                child: const Text(
                  'Cancel',
                  style: TextStyle(color: AppColors.gfGrayText),
                ),
              ),
            ],
          ),
    );

    if (choice == 'original') {
      await _openImageEditorWithOriginal();
    } else if (choice == 'new') {
      await _openImageEditorWithNew();
    } else {
      // User cancelled, go back
      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  Future<void> _openImageEditorWithOriginal() async {
    try {
      AppLogger.debug(
        'ReflexCreationScreen: Opening image editor with original image',
      );

      // Get the original post's media URL
      final mediaUrl = await widget.originalPost.getMediaUrl();
      if (mediaUrl == null) {
        _showError('No image available for this post');
        return;
      }

      await _openProImageEditor(mediaUrl: mediaUrl);
    } catch (e, stackTrace) {
      AppLogger.error(
        'ReflexCreationScreen: Error opening image editor with original',
        error: e,
        stackTrace: stackTrace,
      );
      _showError('Failed to open image editor: $e');
    }
  }

  Future<void> _openImageEditorWithNew() async {
    try {
      AppLogger.debug(
        'ReflexCreationScreen: Opening image editor with new image',
      );

      // Use image picker to select a new image
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image == null) {
        // User cancelled image selection, go back
        if (mounted) {
          Navigator.of(context).pop();
        }
        return;
      }

      // Convert to file path for pro image editor
      await _openProImageEditor(imagePath: image.path);
    } catch (e, stackTrace) {
      AppLogger.error(
        'ReflexCreationScreen: Error opening image editor with new image',
        error: e,
        stackTrace: stackTrace,
      );
      _showError('Failed to open image editor: $e');
    }
  }

  Future<void> _openProImageEditor({
    String? mediaUrl,
    String? imagePath,
  }) async {
    try {
      if (!mounted) return;

      final Widget editor;
      if (mediaUrl != null) {
        editor = ProImageEditor.network(
          mediaUrl,
          callbacks: ProImageEditorCallbacks(
            onImageEditingComplete: (bytes) async {
              Navigator.of(context).pop(bytes);
            },
          ),
        );
      } else if (imagePath != null) {
        editor = ProImageEditor.file(
          File(imagePath),
          callbacks: ProImageEditorCallbacks(
            onImageEditingComplete: (bytes) async {
              Navigator.of(context).pop(bytes);
            },
          ),
        );
      } else {
        _showError('No image source provided');
        return;
      }

      final editedBytes = await Navigator.of(
        context,
      ).push<Uint8List>(MaterialPageRoute(builder: (context) => editor));

      if (editedBytes != null) {
        setState(() {
          _editedImageBytes = editedBytes;
        });
        AppLogger.debug('ReflexCreationScreen: Image edited successfully');
        // Automatically create the reflex after editing
        await _createReflex();
      } else {
        // User cancelled editing, go back
        if (mounted) {
          Navigator.of(context).pop();
        }
      }
    } catch (e, stackTrace) {
      AppLogger.error(
        'ReflexCreationScreen: Error in pro image editor',
        error: e,
        stackTrace: stackTrace,
      );
      _showError('Failed to edit image: $e');
    }
  }

  Future<void> _uploadEditedImage() async {
    if (_editedImageBytes == null) return;

    try {
      AppLogger.debug('ReflexCreationScreen: Uploading edited image');

      // Create a temporary file from the edited bytes
      final tempDir = Directory.systemTemp;
      final tempFile = File(
        '${tempDir.path}/reflex_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );
      await tempFile.writeAsBytes(_editedImageBytes!);

      // Upload the image as reflex media type
      final mediaId = await AwsMediaService.instance.uploadMedia(
        file: tempFile,
        fileName: 'reflex_${DateTime.now().millisecondsSinceEpoch}.jpg',
        fileType: 'image/jpeg',
        mediaType: 'reflex',
      );

      if (mediaId != null) {
        setState(() {
          _uploadedMediaId = mediaId;
        });
        AppLogger.debug(
          'ReflexCreationScreen: Image uploaded with ID: $mediaId',
        );
      } else {
        _showError('Failed to upload image');
      }

      // Clean up temp file
      if (await tempFile.exists()) {
        await tempFile.delete();
      }
    } catch (e, stackTrace) {
      AppLogger.error(
        'ReflexCreationScreen: Error uploading image',
        error: e,
        stackTrace: stackTrace,
      );
      _showError('Failed to upload image: $e');
    }
  }

  Future<void> _createReflex() async {
    if (_isCreating) return;

    // Validate input - we must have edited image bytes
    if (_editedImageBytes == null) {
      _showError('Please edit an image first');
      return;
    }

    setState(() {
      _isCreating = true;
    });

    try {
      AppLogger.debug('ReflexCreationScreen: Creating reflex');

      // Upload image if we have edited bytes but no media ID yet
      if (_uploadedMediaId == null) {
        await _uploadEditedImage();
        if (_uploadedMediaId == null) {
          setState(() {
            _isCreating = false;
          });
          return;
        }
      }

      final request = CreateReflexRequest(
        postId: widget.originalPost.id,
        mediaId: _uploadedMediaId,
        flareData: null, // No flare data for image reflexes
        textOverlay: null, // No text overlay for now
        reflexType:
            ReflexType.customImage, // Always custom image since we edited it
      );

      if (!mounted) return;
      final reflexProvider = Provider.of<ReflexProvider>(
        context,
        listen: false,
      );
      final reflex = await reflexProvider.createReflex(request);

      if (reflex != null) {
        // Update the post's reflex count
        if (!mounted) return;
        final postsProvider = Provider.of<PostsProvider>(
          context,
          listen: false,
        );
        await postsProvider.refreshPost(widget.originalPost.id);

        AppLogger.debug('ReflexCreationScreen: Reflex created successfully');

        if (mounted) {
          Navigator.of(context).pop(reflex);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Reflex created successfully!'),
              backgroundColor: AppColors.gfGreen,
            ),
          );
        }
      } else {
        _showError('Failed to create reflex');
      }
    } catch (e, stackTrace) {
      AppLogger.error(
        'ReflexCreationScreen: Error creating reflex',
        error: e,
        stackTrace: stackTrace,
      );
      _showError('Failed to create reflex: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isCreating = false;
        });
      }
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), backgroundColor: Colors.red),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.gfDarkBackground,
      appBar: AppBar(
        title: const Text(
          'Create Reflex',
          style: TextStyle(
            color: AppColors.gfGreen,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.gfDarkBackground,
        iconTheme: const IconThemeData(color: AppColors.gfOffWhite),
      ),
      body: Center(
        child:
            _isCreating
                ? const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppColors.gfGreen,
                      ),
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Creating your reflex...',
                      style: TextStyle(
                        color: AppColors.gfOffWhite,
                        fontSize: 16,
                      ),
                    ),
                  ],
                )
                : const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.edit, color: AppColors.gfGreen, size: 64),
                    SizedBox(height: 16),
                    Text(
                      'Choose your image source...',
                      style: TextStyle(
                        color: AppColors.gfOffWhite,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
      ),
    );
  }
}
