# GameFlex Mobile Environment Variables

# API Configuration
# Local development: http://127.0.0.1:3000
# Remote AWS API Gateway: https://wc19zcdkec.execute-api.us-west-2.amazonaws.com/v1
# Custom domain (staging/production): https://staging.api.gameflex.io or https://api.gameflex.io
API_BASE_URL=

# CloudFlare R2 Configuration
R2_PUBLIC_URL=

# Sign in with Apple Configuration
# The sign_in_with_apple package handles the authentication flow automatically
# No environment variables needed for frontend - backend handles verification

# Environment
ENVIRONMENT=development