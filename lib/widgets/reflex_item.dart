import 'package:flutter/material.dart';
import '../models/reflex_model.dart';
import '../services/aws_reflex_service.dart';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';

class ReflexItem extends StatefulWidget {
  final ReflexModel reflex;
  final bool isVisible;

  const ReflexItem({super.key, required this.reflex, this.isVisible = true});

  @override
  State<ReflexItem> createState() => _ReflexItemState();
}

class _ReflexItemState extends State<ReflexItem> {
  late bool _isLiked;
  late int _likeCount;
  bool _isLiking = false;

  @override
  void initState() {
    super.initState();
    _isLiked = widget.reflex.isLikedByCurrentUser;
    _likeCount = widget.reflex.likes;
  }

  @override
  void didUpdateWidget(ReflexItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.reflex.id != widget.reflex.id) {
      _isLiked = widget.reflex.isLikedByCurrentUser;
      _likeCount = widget.reflex.likes;
    }
  }

  Future<void> _toggleLike() async {
    if (_isLiking) return; // Prevent multiple simultaneous requests

    setState(() {
      _isLiking = true;
      // Optimistic update
      _isLiked = !_isLiked;
      _likeCount += _isLiked ? 1 : -1;
    });

    try {
      bool success;
      if (_isLiked) {
        success = await AwsReflexService.instance.likeReflex(widget.reflex.id);
      } else {
        success = await AwsReflexService.instance.unlikeReflex(
          widget.reflex.id,
        );
      }

      if (!success) {
        // Revert optimistic update on failure
        setState(() {
          _isLiked = !_isLiked;
          _likeCount += _isLiked ? 1 : -1;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                _isLiked ? 'Failed to like reflex' : 'Failed to unlike reflex',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      AppLogger.error('Error toggling reflex like', error: e);
      // Revert optimistic update on error
      setState(() {
        _isLiked = !_isLiked;
        _likeCount += _isLiked ? 1 : -1;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Network error. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLiking = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black,
      child: Stack(
        children: [
          // Background content
          _buildReflexContent(),

          // Overlay information
          _buildReflexOverlay(),
        ],
      ),
    );
  }

  Widget _buildReflexContent() {
    if (widget.reflex.reflexType == ReflexType.customImage &&
        widget.reflex.mediaId != null) {
      // For custom image reflexes, show the edited image
      return _buildImageContent();
    } else {
      // For flare reflexes, show a gradient background with flare effects
      return _buildFlareContent();
    }
  }

  Widget _buildImageContent() {
    return FutureBuilder<String?>(
      future: widget.reflex.getMediaUrl(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            width: double.infinity,
            height: double.infinity,
            color: Colors.black,
            child: const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
              ),
            ),
          );
        }

        final mediaUrl = snapshot.data;
        if (mediaUrl != null && mediaUrl.isNotEmpty) {
          return SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: Image.network(
              mediaUrl,
              fit: BoxFit.fitWidth,
              errorBuilder: (context, error, stackTrace) {
                return _buildImagePlaceholder();
              },
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  width: double.infinity,
                  height: double.infinity,
                  color: Colors.black,
                  child: Center(
                    child: CircularProgressIndicator(
                      value:
                          loadingProgress.expectedTotalBytes != null
                              ? loadingProgress.cumulativeBytesLoaded /
                                  loadingProgress.expectedTotalBytes!
                              : null,
                      valueColor: const AlwaysStoppedAnimation<Color>(
                        AppColors.gfGreen,
                      ),
                    ),
                  ),
                );
              },
            ),
          );
        }

        return _buildImagePlaceholder();
      },
    );
  }

  Widget _buildImagePlaceholder() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.gfTeal, AppColors.gfDarkBlue],
        ),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.image, color: AppColors.gfOffWhite, size: 64),
            SizedBox(height: 16),
            Text(
              'Custom Image Reflex',
              style: TextStyle(
                color: AppColors.gfOffWhite,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFlareContent() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [AppColors.gfDarkBlue, AppColors.gfTeal, AppColors.gfGreen],
        ),
      ),
      child: Stack(
        children: [
          // Animated flare effects
          _buildFlareEffects(),

          // Center content
          const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.auto_awesome, color: AppColors.gfOffWhite, size: 64),
                SizedBox(height: 16),
                Text(
                  'Flare Reflex',
                  style: TextStyle(
                    color: AppColors.gfOffWhite,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFlareEffects() {
    // TODO: Implement actual flare effects based on widget.reflex.flareData
    // For now, show some animated particles
    return Stack(
      children: [
        // Animated particles or effects can be added here
        Positioned(top: 100, left: 50, child: _buildFlareParticle()),
        Positioned(top: 200, right: 80, child: _buildFlareParticle()),
        Positioned(bottom: 150, left: 100, child: _buildFlareParticle()),
      ],
    );
  }

  Widget _buildFlareParticle() {
    return Container(
      width: 20,
      height: 20,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppColors.gfGreen.withValues(alpha: 0.7),
        boxShadow: [
          BoxShadow(
            color: AppColors.gfGreen.withValues(alpha: 0.5),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
    );
  }

  Widget _buildReflexOverlay() {
    return Positioned(
      left: 16,
      right: 16,
      bottom: 100,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Text overlay if present
          if (widget.reflex.textOverlay != null &&
              widget.reflex.textOverlay!.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                widget.reflex.textOverlay!,
                style: const TextStyle(
                  color: AppColors.gfOffWhite,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Author information
          _buildAuthorInfo(),

          const SizedBox(height: 8),

          // Reflex metadata
          _buildReflexMetadata(),
        ],
      ),
    );
  }

  Widget _buildAuthorInfo() {
    return Row(
      children: [
        // Avatar placeholder
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: AppColors.gfTeal,
            border: Border.all(color: AppColors.gfGreen, width: 2),
          ),
          child:
              widget.reflex.avatarUrl != null
                  ? ClipOval(
                    child: Image.network(
                      widget.reflex.avatarUrl!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return const Icon(
                          Icons.person,
                          color: AppColors.gfOffWhite,
                          size: 24,
                        );
                      },
                    ),
                  )
                  : const Icon(
                    Icons.person,
                    color: AppColors.gfOffWhite,
                    size: 24,
                  ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.reflex.authorDisplayName,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 16,
                  shadows: [
                    Shadow(
                      color: Colors.black,
                      blurRadius: 2,
                      offset: Offset(1, 1),
                    ),
                  ],
                ),
              ),
              Text(
                widget.reflex.authorUsername,
                style: const TextStyle(
                  color: AppColors.gfGrayText,
                  fontSize: 14,
                  shadows: [
                    Shadow(
                      color: Colors.black,
                      blurRadius: 2,
                      offset: Offset(1, 1),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildReflexMetadata() {
    return Row(
      children: [
        // Reflex type indicator
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: AppColors.gfGreen.withValues(alpha: 0.8),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            widget.reflex.reflexType == ReflexType.flare ? 'FLARE' : 'CUSTOM',
            style: const TextStyle(
              color: Colors.black,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(width: 12),

        // Time ago
        Text(
          widget.reflex.timeAgo,
          style: const TextStyle(
            color: AppColors.gfGrayText,
            fontSize: 12,
            shadows: [
              Shadow(color: Colors.black, blurRadius: 2, offset: Offset(1, 1)),
            ],
          ),
        ),

        const Spacer(),

        // Like button
        _buildLikeButton(),

        const Spacer(),

        // Reflex icon
        const Icon(
          Icons.sports_martial_arts,
          color: AppColors.gfGreen,
          size: 20,
        ),
      ],
    );
  }

  Widget _buildLikeButton() {
    return GestureDetector(
      onTap: _isLiking ? null : _toggleLike,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.6),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color:
                _isLiked
                    ? AppColors.gfGreen
                    : AppColors.gfGrayText.withValues(alpha: 0.5),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _isLiking
                ? SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _isLiked ? AppColors.gfGreen : AppColors.gfOffWhite,
                    ),
                  ),
                )
                : Icon(
                  _isLiked ? Icons.favorite : Icons.favorite_border,
                  color: _isLiked ? AppColors.gfGreen : AppColors.gfOffWhite,
                  size: 16,
                ),
            const SizedBox(width: 4),
            Text(
              '$_likeCount',
              style: TextStyle(
                color: _isLiked ? AppColors.gfGreen : AppColors.gfOffWhite,
                fontSize: 12,
                fontWeight: FontWeight.w500,
                shadows: const [
                  Shadow(
                    color: Colors.black,
                    blurRadius: 2,
                    offset: Offset(1, 1),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
