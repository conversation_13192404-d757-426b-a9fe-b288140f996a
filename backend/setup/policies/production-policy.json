{"Version": "2012-10-17", "Statement": [{"Sid": "CloudFormationAccess", "Effect": "Allow", "Action": "cloudformation:*", "Resource": ["arn:aws:cloudformation:*:*:stack/gameflex-prod*/*", "arn:aws:cloudformation:*:*:stack/CDKToolkit-GameFlex-Production/*"]}, {"Sid": "CloudFormationGlobal", "Effect": "Allow", "Action": ["cloudformation:ListStacks", "cloudformation:DescribeStacks"], "Resource": "*"}, {"Sid": "S3Access", "Effect": "Allow", "Action": "s3:*", "Resource": ["arn:aws:s3:::gameflex-prod*", "arn:aws:s3:::gameflex-prod*/*", "arn:aws:s3:::cdk-gfprd-*", "arn:aws:s3:::cdk-gfprd-*/*", "arn:aws:s3:::cdk-gfprod-*", "arn:aws:s3:::cdk-gfprod-*/*", "arn:aws:s3:::cdk-gfproduction-*", "arn:aws:s3:::cdk-gfproduction-*/*"]}, {"Sid": "S3Global", "Effect": "Allow", "Action": "s3:ListAllMyBuckets", "Resource": "*"}, {"Sid": "LambdaAccess", "Effect": "Allow", "Action": "lambda:*", "Resource": "arn:aws:lambda:*:*:function:gameflex-prod*"}, {"Sid": "DynamoDBAccess", "Effect": "Allow", "Action": "dynamodb:*", "Resource": ["arn:aws:dynamodb:*:*:table/gameflex-prod*", "arn:aws:dynamodb:*:*:table/gameflex-prod*/index/*"]}, {"Sid": "IAMRoleAccess", "Effect": "Allow", "Action": "iam:*", "Resource": ["arn:aws:iam::*:role/gameflex-prod*", "arn:aws:iam::*:role/cdk-gfprd-*", "arn:aws:iam::*:role/cdk-gfprod-*", "arn:aws:iam::*:role/cdk-gfproduction-*"]}, {"Sid": "CognitoAccess", "Effect": "Allow", "Action": "cognito-idp:*", "Resource": "arn:aws:cognito-idp:us-east-1:*:userpool/*"}, {"Sid": "APIGatewayAccess", "Effect": "Allow", "Action": "apigateway:*", "Resource": ["arn:aws:apigateway:us-east-1::/restapis*", "arn:aws:apigateway:us-east-1::/domainnames*", "arn:aws:apigateway:us-east-1::/domainnames/*/basepathmappings*"]}, {"Sid": "CertificateManagerAccess", "Effect": "Allow", "Action": ["acm:DescribeCertificate", "acm:ListCertificates"], "Resource": "*"}, {"Sid": "CloudFrontAccess", "Effect": "Allow", "Action": ["cloudfront:UpdateDistribution", "cloudfront:GetDistribution", "cloudfront:CreateDistribution", "cloudfront:DeleteDistribution", "cloudfront:ListDistributions", "cloudfront:TagResource", "cloudfront:UntagResource"], "Resource": "*"}, {"Sid": "SecretsManagerAccess", "Effect": "Allow", "Action": "secretsmanager:*", "Resource": ["arn:aws:secretsmanager:*:*:secret:gameflex-*-production*", "arn:aws:secretsmanager:*:*:secret:gameflex-r2-config-production*", "arn:aws:secretsmanager:*:*:secret:gameflex-app-config-production*"]}, {"Sid": "LogsAccess", "Effect": "Allow", "Action": "logs:*", "Resource": "arn:aws:logs:*:*:log-group:/aws/lambda/gameflex-prod*"}, {"Sid": "ECRAccess", "Effect": "Allow", "Action": "ecr:*", "Resource": ["arn:aws:ecr:*:*:repository/cdk-gfprd-*", "arn:aws:ecr:*:*:repository/cdk-gfprod-*", "arn:aws:ecr:*:*:repository/cdk-gfproduction-*"]}, {"Sid": "SSMAccess", "Effect": "Allow", "Action": "ssm:*", "Resource": "arn:aws:ssm:*:*:parameter/cdk-bootstrap/*"}, {"Sid": "STSAccess", "Effect": "Allow", "Action": ["sts:GetCallerIdentity", "sts:<PERSON><PERSON>Role"], "Resource": "*"}, {"Sid": "DenyOtherEnvironments", "Effect": "<PERSON><PERSON>", "Action": "*", "Resource": ["arn:aws:cloudformation:*:*:stack/gameflex-dev*/*", "arn:aws:cloudformation:*:*:stack/gameflex-staging*/*", "arn:aws:s3:::gameflex-dev*", "arn:aws:s3:::gameflex-staging*", "arn:aws:lambda:*:*:function:gameflex-dev*", "arn:aws:lambda:*:*:function:gameflex-staging*", "arn:aws:dynamodb:*:*:table/gameflex-dev*", "arn:aws:dynamodb:*:*:table/gameflex-staging*", "arn:aws:iam::*:role/gameflex-dev*", "arn:aws:iam::*:role/gameflex-staging*"]}]}