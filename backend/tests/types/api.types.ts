// API Response Types
export interface ApiResponse<T = any> {
  statusCode: number;
  data?: T;
  error?: string;
  message?: string;
}

// Auth Types
export interface SignUpRequest {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

export interface SignUpResponse {
  message: string;
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
}

export interface SignInRequest {
  email: string;
  password: string;
}

export interface SignInResponse {
  message: string;
  requiresUsername?: boolean;
  tokens: {
    accessToken: string;
    refreshToken: string;
    idToken: string;
  };
  user: {
    id: string;
    email: string;
    username?: string;
    firstName: string;
    lastName: string;
  };
}

export interface SetUsernameRequest {
  username: string;
}

export interface SetUsernameResponse {
  message: string;
  user: {
    id: string;
    email: string;
    username: string;
    firstName: string;
    lastName: string;
  };
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  message: string;
  tokens: {
    accessToken: string;
    idToken: string;
  };
}

export interface ValidateTokenResponse {
  message: string;
  valid: boolean;
  user?: {
    id: string;
    email: string;
    username: string;
    firstName: string;
    lastName: string;
  };
}

// Test User Data
export interface TestUser {
  id?: string;
  email: string;
  password: string;
  username: string;
  firstName: string;
  lastName: string;
  tokens?: {
    accessToken: string;
    refreshToken: string;
    idToken: string;
  };
}

// Global Test Context
export interface GlobalTestContext {
  testUser: TestUser | null;
  apiBaseUrl: string;
  isSetup: boolean;
}

// HTTP Client Types
export interface HttpClientConfig {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
}

export interface HttpResponse<T = any> {
  status: number;
  statusText: string;
  data: T;
  headers: Record<string, string>;
}

// Test Configuration
export interface TestConfig {
  apiBaseUrl: string;
  testUserEmail: string;
  testUserPassword: string;
  testUserUsername: string;
  testUserFirstName: string;
  testUserLastName: string;
  timeout: number;
}

// Health Types
export interface HealthResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  environment: string;
  version: string;
  services: {
    database: 'healthy' | 'unhealthy' | 'skipped';
    api: 'healthy';
  };
  uptime: number;
  memory: NodeJS.MemoryUsage;
  environment_variables: {
    USER_POOL_ID?: string;
    USER_POOL_CLIENT_ID?: string;
    USERS_TABLE?: string;
    POSTS_TABLE?: string;
    MEDIA_BUCKET?: string;
  };
}

// User Types
export interface UserProfile {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  displayName?: string;
  bio?: string;
  avatarUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
  displayName?: string;
  bio?: string;
  avatarUrl?: string;
}

export interface UserProfileResponse {
  message: string;
  user: UserProfile;
}

export interface FollowResponse {
  message: string;
  following: boolean;
}

// Channel Types
export interface Channel {
  id: string;
  name: string;
  description?: string;
  isPublic: boolean;
  createdBy: string;
  memberCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateChannelRequest {
  name: string;
  description?: string;
  isPublic?: boolean;
}

export interface ChannelResponse {
  message: string;
  channel: Channel;
}

export interface ChannelsListResponse {
  channels: Channel[];
}

// Media Types
export interface MediaRecord {
  id: string;
  filename: string;
  contentType: string;
  size: number;
  url?: string;
  uploadUrl?: string;
  status: 'pending' | 'uploaded' | 'failed';
  userId: string;
  createdAt: string;
  updatedAt: string;
}

export interface UploadMediaRequest {
  filename: string;
  contentType: string;
  size: number;
}

export interface UploadMediaResponse {
  message: string;
  media: MediaRecord;
}

// Reflex Types
export interface Reflex {
  id: string;
  postId: string;
  userId: string;
  mediaId?: string;
  flareData?: any;
  textOverlay?: string;
  reflexType: 'flare' | 'custom_image';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  username?: string;
  displayName?: string;
  avatarUrl?: string;
}

export interface CreateReflexRequest {
  postId: string;
  mediaId?: string;
  flareData?: any;
  textOverlay?: string;
  reflexType?: 'flare' | 'custom_image';
}

export interface ReflexResponse {
  message: string;
  reflex: Reflex;
}

export interface ReflexesListResponse {
  reflexes: Reflex[];
}
