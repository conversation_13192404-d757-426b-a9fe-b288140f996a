import 'dart:io';
import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;
import '../theme/app_theme.dart';
import '../services/aws_posts_service.dart';
import '../services/aws_media_service.dart';
import '../utils/app_logger.dart';

class PostCompositionScreen extends StatefulWidget {
  final File? croppedImageFile;

  const PostCompositionScreen({super.key, this.croppedImageFile});

  @override
  State<PostCompositionScreen> createState() => _PostCompositionScreenState();
}

class _PostCompositionScreenState extends State<PostCompositionScreen> {
  final TextEditingController _textController = TextEditingController();
  bool _isPosting = false;
  final int _maxCharacters = 500;

  // Multi-step post creation state
  String _statusMessage = '';

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  Future<void> _submitPost() async {
    if (_isPosting) return;

    setState(() {
      _isPosting = true;
      _statusMessage = 'Creating draft post...';
    });

    try {
      // Step 1: Create draft post
      final draftPost = await AwsPostsService.instance.createDraftPost(
        content: _textController.text.trim(),
      );

      if (draftPost == null) {
        throw Exception('Failed to create draft post');
      }

      setState(() {
        _statusMessage = 'Uploading media...';
      });

      // Step 2: Upload media if image is provided
      if (widget.croppedImageFile != null) {
        final fileName = path.basename(widget.croppedImageFile!.path);
        final fileExtension = path.extension(fileName).toLowerCase();

        String mimeType;
        switch (fileExtension) {
          case '.jpg':
          case '.jpeg':
            mimeType = 'image/jpeg';
            break;
          case '.png':
            mimeType = 'image/png';
            break;
          case '.gif':
            mimeType = 'image/gif';
            break;
          default:
            mimeType = 'image/jpeg';
        }

        final mediaId = await AwsMediaService.instance.uploadMedia(
          file: widget.croppedImageFile!,
          fileName: fileName,
          fileType: mimeType,
          mediaType: 'image',
        );

        if (mediaId == null) {
          throw Exception('Failed to upload media');
        }

        setState(() {
          _statusMessage = 'Verifying media upload...';
        });

        // Step 3: Verify media status before attaching
        final mediaInfo = await AwsMediaService.instance.getMedia(mediaId);
        if (mediaInfo != null) {
          final mediaStatus = mediaInfo['status'] as String?;
          developer.log('Media status before attaching: $mediaStatus');
        }

        setState(() {
          _statusMessage = 'Attaching media to post...';
        });

        // Step 4: Attach media to post
        final updatedPost = await AwsPostsService.instance.attachMediaToPost(
          postId: draftPost.id,
          mediaId: mediaId,
        );

        if (updatedPost == null) {
          throw Exception('Failed to attach media to post');
        }
      }

      setState(() {
        _statusMessage = 'Publishing post...';
      });

      // Step 4: Publish the post
      final publishedPost = await AwsPostsService.instance.publishPost(
        draftPost.id,
      );

      if (publishedPost == null) {
        throw Exception('Failed to publish post');
      }

      if (mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Post created successfully!'),
            backgroundColor: AppColors.gfGreen,
          ),
        );

        // Navigate back to home screen (pop all the way back)
        Navigator.of(context).popUntil((route) => route.isFirst);
      }
    } catch (e) {
      AppLogger.error('Error creating post', error: e);
      _showErrorSnackBar(
        'An error occurred while creating your post: ${e.toString()}',
      );
    } finally {
      if (mounted) {
        setState(() {
          _isPosting = false;
          _statusMessage = '';
        });
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final remainingCharacters = _maxCharacters - _textController.text.length;

    return Scaffold(
      backgroundColor: AppColors.darkBlue,
      appBar: AppBar(
        title: const Text(
          'Create Post',
          style: TextStyle(
            color: AppColors.gfGreen,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.darkBlue,
        iconTheme: const IconThemeData(color: AppColors.gfOffWhite),
        elevation: 0,
        actions: [
          if (!_isPosting)
            TextButton(
              onPressed:
                  _textController.text.trim().isNotEmpty ? _submitPost : null,
              child: Text(
                'Post',
                style: TextStyle(
                  color:
                      _textController.text.trim().isNotEmpty
                          ? AppColors.gfGreen
                          : AppColors.gfGrayText,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image preview (if image is provided)
              if (widget.croppedImageFile != null)
                Container(
                  width: double.infinity,
                  height: 200,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.gfGrayBorder),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.file(
                      widget.croppedImageFile!,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),

              if (widget.croppedImageFile != null) const SizedBox(height: 24),

              // Text input label
              Text(
                widget.croppedImageFile != null
                    ? 'Add a caption'
                    : 'What\'s on your mind?',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.gfOffWhite,
                ),
              ),

              const SizedBox(height: 12),

              // Text input field
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.gfCardBackground,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.gfGrayBorder),
                  ),
                  child: TextField(
                    controller: _textController,
                    maxLines: null,
                    maxLength: _maxCharacters,
                    style: const TextStyle(
                      color: AppColors.gfOffWhite,
                      fontSize: 16,
                    ),
                    decoration: const InputDecoration(
                      hintText: 'What\'s on your mind?',
                      hintStyle: TextStyle(
                        color: AppColors.gfGrayText,
                        fontSize: 16,
                      ),
                      border: InputBorder.none,
                      counterText: '', // Hide the built-in counter
                    ),
                    onChanged: (text) {
                      setState(
                        () {},
                      ); // Rebuild to update character count and button state
                    },
                  ),
                ),
              ),

              const SizedBox(height: 12),

              // Character counter
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '$remainingCharacters characters remaining',
                    style: TextStyle(
                      color:
                          remainingCharacters < 50
                              ? Colors.orange
                              : AppColors.gfGrayText,
                      fontSize: 14,
                    ),
                  ),
                  if (_isPosting)
                    const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        color: AppColors.gfGreen,
                        strokeWidth: 2,
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 24),

              // Submit button
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed:
                      _isPosting || _textController.text.trim().isEmpty
                          ? null
                          : _submitPost,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.gfGreen,
                    foregroundColor: AppColors.darkBlue,
                    disabledBackgroundColor: AppColors.gfGrayBorder,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child:
                      _isPosting
                          ? Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  color: AppColors.darkBlue,
                                  strokeWidth: 2,
                                ),
                              ),
                              if (_statusMessage.isNotEmpty) ...[
                                const SizedBox(height: 4),
                                Text(
                                  _statusMessage,
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: AppColors.darkBlue,
                                  ),
                                ),
                              ],
                            ],
                          )
                          : const Text(
                            'Create Post',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
